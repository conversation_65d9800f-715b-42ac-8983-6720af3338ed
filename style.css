* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Mulish', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: #458FF6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-link {
    text-decoration: none;
    color: #1F1534;
    font-weight: 400;
    opacity: 0.5;
    transition: opacity 0.3s;
}

.nav-link.active,
.nav-link:hover {
    opacity: 1;
    font-weight: 600;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 21px;
    color: #7D7987;
    margin-bottom: 40px;
    line-height: 1.5;
}

.btn-primary {
    background: #458FF6;
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 55px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #357abd;
}

/* Illustration Styles */
.hero-image {
    position: relative;
    height: 500px;
}

.illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

.heart-icon {
    position: absolute;
    top: 20px;
    left: 50px;
    width: 80px;
    height: 80px;
}

.heart-svg {
    width: 100%;
    height: 100%;
}

.heartbeat-line {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 100px;
    height: 20px;
    transform: translateY(-50%);
}

.heartbeat-svg {
    width: 100%;
    height: 100%;
}

.medical-card {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 150px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 20px;
}

.card-header {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.profile-icon {
    width: 30px;
    height: 30px;
    background: #458FF6;
    border-radius: 50%;
}

.card-lines {
    flex: 1;
}

.line {
    height: 4px;
    background: #E5E5E5;
    margin-bottom: 5px;
    border-radius: 2px;
}

.line:first-child {
    width: 80%;
}

.line:nth-child(2) {
    width: 60%;
}

.line:last-child {
    width: 40%;
}

.chart-area {
    height: 60px;
}

.chart-svg {
    width: 100%;
    height: 100%;
}

.shield-icon {
    position: absolute;
    top: 80px;
    right: 80px;
    width: 60px;
    height: 70px;
}

.shield-svg {
    width: 100%;
    height: 100%;
}

.people {
    position: absolute;
    bottom: 50px;
    left: 20px;
}

.person {
    position: absolute;
    width: 40px;
    height: 80px;
}

.person-1 {
    left: 0;
}

.person-2 {
    left: 30px;
}

.person-head {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin: 0 auto 5px;
}

.person-1 .person-head {
    background: #4A90E2;
}

.person-2 .person-head {
    background: #FF6B9D;
}

.person-body {
    width: 40px;
    height: 50px;
    border-radius: 20px 20px 0 0;
}

.person-1 .person-body {
    background: #4A90E2;
}

.person-2 .person-body {
    background: #FF6B9D;
}

.medical-bag {
    position: absolute;
    bottom: 100px;
    right: 50px;
    width: 50px;
    height: 40px;
}

.bag-body {
    width: 50px;
    height: 35px;
    background: #4A90E2;
    border-radius: 5px;
    position: relative;
}

.bag-handle {
    width: 30px;
    height: 10px;
    background: #4A90E2;
    border-radius: 5px 5px 0 0;
    margin: 0 auto;
    margin-bottom: -5px;
}

.cross {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    font-weight: bold;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #458FF6;
    border-radius: 50%;
    opacity: 0.6;
}

.dot-1 {
    top: 30%;
    left: 20%;
}

.dot-2 {
    top: 70%;
    right: 30%;
}

.dot-3 {
    bottom: 20%;
    left: 60%;
}

.plus {
    position: absolute;
    color: #458FF6;
    font-size: 20px;
    font-weight: bold;
    opacity: 0.6;
}

.plus-1 {
    top: 20%;
    right: 20%;
}

.plus-2 {
    bottom: 30%;
    left: 30%;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
}

.title-underline {
    width: 56px;
    height: 2px;
    background: #000;
    margin: 0 auto 30px;
}

.section-description {
    font-size: 18px;
    color: #7D7987;
    line-height: 1.6;
    margin-bottom: 60px;
}

/* Services Grid */
.services-grid {
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 60px 40px;
    margin-bottom: 60px;
    border: 1px solid #f5f5f5;
}

.service-card {
    background: transparent;
    padding: 0;
    border-radius: 0;
    text-align: left;
    transition: transform 0.3s;
    border: none;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #E8F4FD 0%, #D6EFFF 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.icon-svg {
    width: 50px;
    height: 50px;
}

.service-title {
    font-size: 24px;
    font-weight: 700;
    color: #000;
    margin-bottom: 15px;
}

.service-description {
    font-size: 16px;
    color: #7D7987;
    line-height: 1.6;
}

.services-footer {
    text-align: center;
}

.btn-secondary {
    background: transparent;
    color: #458FF6;
    border: 1.4px solid #458FF6;
    padding: 15px 40px;
    border-radius: 55px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background: #458FF6;
    color: white;
}

/* Leading Healthcare Section */
.leading-healthcare {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.healthcare-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.healthcare-image {
    position: relative;
    height: 500px;
}

.healthcare-illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Medical Tablet */
.medical-tablet {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 280px;
    height: 200px;
    background: #458FF6;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 15px 35px rgba(69, 143, 246, 0.3);
}

.tablet-screen {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 10px;
    padding: 15px;
}

.screen-header {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.profile-avatar {
    width: 25px;
    height: 25px;
    background: #458FF6;
    border-radius: 50%;
}

.header-lines {
    flex: 1;
}

.header-line {
    height: 3px;
    background: #E5E5E5;
    margin-bottom: 4px;
    border-radius: 2px;
}

.header-line.short {
    width: 60%;
}

.chart-section {
    height: 80px;
    margin-bottom: 15px;
}

.tablet-chart {
    width: 100%;
    height: 100%;
}

.data-rows {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.data-row {
    height: 3px;
    background: #E5E5E5;
    border-radius: 2px;
}

.data-row:first-child {
    width: 80%;
}

.data-row:nth-child(2) {
    width: 65%;
}

.data-row:last-child {
    width: 45%;
}

/* Medicine Bottles */
.medicine-bottles {
    position: absolute;
    top: 20%;
    right: 10%;
}

.bottle {
    position: absolute;
    width: 40px;
    height: 60px;
}

.bottle-1 {
    left: 0;
    top: 0;
}

.bottle-2 {
    left: 25px;
    top: 15px;
}

.bottle-cap {
    width: 100%;
    height: 12px;
    background: #4A90E2;
    border-radius: 6px 6px 0 0;
}

.bottle-body {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, #87CEEB 0%, #4A90E2 100%);
    border-radius: 0 0 8px 8px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bottle-label {
    width: 25px;
    height: 25px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cross-symbol {
    color: #4A90E2;
    font-weight: bold;
    font-size: 14px;
}

/* Floating Card */
.floating-card {
    position: absolute;
    bottom: 20%;
    right: 20%;
    width: 80px;
    height: 50px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
}

.card-icon {
    width: 20px;
    height: 20px;
    background: #458FF6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.card-content {
    flex: 1;
}

.card-line {
    height: 2px;
    background: #E5E5E5;
    margin-bottom: 3px;
    border-radius: 1px;
}

.card-line.short {
    width: 70%;
}

/* Healthcare People */
.healthcare-people {
    position: absolute;
    bottom: 10%;
    left: 10%;
}

.healthcare-person-1 {
    position: absolute;
    left: 0;
    top: 0;
}

.healthcare-person-2 {
    position: absolute;
    left: 35px;
    top: 10px;
}

.healthcare-person-1 .person-head {
    background: #FF6B9D;
}

.healthcare-person-1 .person-body {
    background: #FF6B9D;
}

.healthcare-person-1 .person-legs {
    background: #FF6B9D;
}

.healthcare-person-2 .person-head {
    background: #4A90E2;
}

.healthcare-person-2 .person-body {
    background: #4A90E2;
}

.healthcare-person-2 .person-legs {
    background: #4A90E2;
}

.person-legs {
    width: 30px;
    height: 25px;
    border-radius: 0 0 15px 15px;
    margin: 0 auto;
}

/* Background Elements */
.background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
}

.bg-circle-1 {
    width: 150px;
    height: 150px;
    background: #458FF6;
    top: 10%;
    left: 5%;
}

.bg-circle-2 {
    width: 100px;
    height: 100px;
    background: #FF6B9D;
    bottom: 15%;
    right: 5%;
}

.bg-dots {
    position: absolute;
    top: 30%;
    right: 30%;
}

.bg-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #458FF6;
    border-radius: 50%;
    opacity: 0.4;
}

.bg-dot:nth-child(1) {
    top: 0;
    left: 0;
}

.bg-dot:nth-child(2) {
    top: 0;
    left: 15px;
}

.bg-dot:nth-child(3) {
    top: 15px;
    left: 0;
}

.bg-dot:nth-child(4) {
    top: 15px;
    left: 15px;
}

/* Healthcare Text */
.healthcare-text {
    padding-left: 20px;
}

.healthcare-title {
    font-size: 36px;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    line-height: 1.2;
}

.healthcare-underline {
    width: 56px;
    height: 2px;
    background: #000;
    margin-bottom: 30px;
}

.healthcare-description {
    font-size: 18px;
    color: #7D7987;
    line-height: 1.6;
    margin-bottom: 40px;
}



/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 36px;
    }

    .nav-menu {
        display: none;
    }

    .illustration {
        height: 300px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        padding: 40px 20px;
    }

    .service-card {
        padding: 0;
        text-align: center;
    }

    .section-title {
        font-size: 28px;
    }

    .section-description {
        font-size: 16px;
        margin-bottom: 40px;
    }

    .healthcare-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .healthcare-image {
        height: 350px;
        order: 2;
    }

    .healthcare-text {
        order: 1;
        padding-left: 0;
    }

    .healthcare-title {
        font-size: 28px;
    }

    .healthcare-description {
        font-size: 16px;
    }

    .medical-tablet {
        width: 220px;
        height: 160px;
    }

    .medicine-bottles {
        transform: scale(0.8);
    }

    .floating-card {
        transform: scale(0.8);
    }
}