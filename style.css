* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Mulish', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: #458FF6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-link {
    text-decoration: none;
    color: #1F1534;
    font-weight: 400;
    opacity: 0.5;
    transition: opacity 0.3s;
}

.nav-link.active,
.nav-link:hover {
    opacity: 1;
    font-weight: 600;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 21px;
    color: #7D7987;
    margin-bottom: 40px;
    line-height: 1.5;
}

.btn-primary {
    background: #458FF6;
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 55px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #357abd;
}

/* Illustration Styles */
.hero-image {
    position: relative;
    height: 500px;
}

.illustration {
    position: relative;
    width: 100%;
    height: 100%;
}

.heart-icon {
    position: absolute;
    top: 20px;
    left: 50px;
    width: 80px;
    height: 80px;
}

.heart-svg {
    width: 100%;
    height: 100%;
}

.heartbeat-line {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 100px;
    height: 20px;
    transform: translateY(-50%);
}

.heartbeat-svg {
    width: 100%;
    height: 100%;
}

.medical-card {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 150px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 20px;
}

.card-header {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.profile-icon {
    width: 30px;
    height: 30px;
    background: #458FF6;
    border-radius: 50%;
}

.card-lines {
    flex: 1;
}

.line {
    height: 4px;
    background: #E5E5E5;
    margin-bottom: 5px;
    border-radius: 2px;
}

.line:first-child {
    width: 80%;
}

.line:nth-child(2) {
    width: 60%;
}

.line:last-child {
    width: 40%;
}

.chart-area {
    height: 60px;
}

.chart-svg {
    width: 100%;
    height: 100%;
}

.shield-icon {
    position: absolute;
    top: 80px;
    right: 80px;
    width: 60px;
    height: 70px;
}

.shield-svg {
    width: 100%;
    height: 100%;
}

.people {
    position: absolute;
    bottom: 50px;
    left: 20px;
}

.person {
    position: absolute;
    width: 40px;
    height: 80px;
}

.person-1 {
    left: 0;
}

.person-2 {
    left: 30px;
}

.person-head {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin: 0 auto 5px;
}

.person-1 .person-head {
    background: #4A90E2;
}

.person-2 .person-head {
    background: #FF6B9D;
}

.person-body {
    width: 40px;
    height: 50px;
    border-radius: 20px 20px 0 0;
}

.person-1 .person-body {
    background: #4A90E2;
}

.person-2 .person-body {
    background: #FF6B9D;
}

.medical-bag {
    position: absolute;
    bottom: 100px;
    right: 50px;
    width: 50px;
    height: 40px;
}

.bag-body {
    width: 50px;
    height: 35px;
    background: #4A90E2;
    border-radius: 5px;
    position: relative;
}

.bag-handle {
    width: 30px;
    height: 10px;
    background: #4A90E2;
    border-radius: 5px 5px 0 0;
    margin: 0 auto;
    margin-bottom: -5px;
}

.cross {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    font-weight: bold;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #458FF6;
    border-radius: 50%;
    opacity: 0.6;
}

.dot-1 {
    top: 30%;
    left: 20%;
}

.dot-2 {
    top: 70%;
    right: 30%;
}

.dot-3 {
    bottom: 20%;
    left: 60%;
}

.plus {
    position: absolute;
    color: #458FF6;
    font-size: 20px;
    font-weight: bold;
    opacity: 0.6;
}

.plus-1 {
    top: 20%;
    right: 20%;
}

.plus-2 {
    bottom: 30%;
    left: 30%;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
}

.title-underline {
    width: 56px;
    height: 2px;
    background: #000;
    margin: 0 auto 30px;
}

.section-description {
    font-size: 18px;
    color: #7D7987;
    line-height: 1.6;
    margin-bottom: 60px;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.service-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: left;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #f0f0f0;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.service-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #E8F4FD 0%, #D6EFFF 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.icon-svg {
    width: 50px;
    height: 50px;
}

.service-title {
    font-size: 24px;
    font-weight: 700;
    color: #000;
    margin-bottom: 15px;
}

.service-description {
    font-size: 16px;
    color: #7D7987;
    line-height: 1.6;
}

.services-footer {
    text-align: center;
}

.btn-secondary {
    background: transparent;
    color: #458FF6;
    border: 1.4px solid #458FF6;
    padding: 15px 40px;
    border-radius: 55px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background: #458FF6;
    color: white;
}



/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 36px;
    }

    .nav-menu {
        display: none;
    }

    .illustration {
        height: 300px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .service-card {
        padding: 30px 20px;
        text-align: center;
    }

    .section-title {
        font-size: 28px;
    }

    .section-description {
        font-size: 16px;
        margin-bottom: 40px;
    }
}