<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trafalgar - Virtual Healthcare</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-brand">
                <div class="logo">
                    <span class="logo-icon">T</span>
                    <span class="logo-text">Trafalgar</span>
                </div>
            </div>
            <ul class="nav-menu">
                <li><a href="#" class="nav-link active">Home</a></li>
                <li><a href="#" class="nav-link">Find a doctor</a></li>
                <li><a href="#" class="nav-link">Apps</a></li>
                <li><a href="#" class="nav-link">Testimonials</a></li>
                <li><a href="#" class="nav-link">About us</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">Virtual healthcare for you</h1>
                    <p class="hero-description">
                        Trafalgar provides progressive, and affordable healthcare, accessible on mobile and online for everyone
                    </p>
                    <button class="btn-primary">Consult today</button>
                </div>
                <div class="hero-image">
                    <div class="illustration">
                        <!-- Medical illustration elements -->
                        <div class="heart-icon">
                            <svg viewBox="0 0 100 100" class="heart-svg">
                                <path d="M50 85c-2.5 0-4.8-1-6.5-2.8L15 52.5C5.8 43.3 5.8 28.7 15 19.5c9.2-9.2 24.2-9.2 33.4 0L50 21.1l1.6-1.6c9.2-9.2 24.2-9.2 33.4 0 9.2 9.2 9.2 24.2 0 33.4L56.5 82.2C54.8 84 52.5 85 50 85z" fill="#FF6B9D"/>
                            </svg>
                            <div class="heartbeat-line">
                                <svg viewBox="0 0 200 50" class="heartbeat-svg">
                                    <polyline points="0,25 20,25 25,10 35,40 45,15 55,35 65,25 200,25" stroke="#fff" stroke-width="2" fill="none"/>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="medical-card">
                            <div class="card-header">
                                <div class="profile-icon"></div>
                                <div class="card-lines">
                                    <div class="line"></div>
                                    <div class="line"></div>
                                    <div class="line"></div>
                                </div>
                            </div>
                            <div class="chart-area">
                                <svg viewBox="0 0 150 60" class="chart-svg">
                                    <polyline points="10,50 30,30 50,40 70,20 90,35 110,15 130,25" stroke="#4A90E2" stroke-width="2" fill="none"/>
                                </svg>
                            </div>
                        </div>

                        <div class="shield-icon">
                            <svg viewBox="0 0 60 70" class="shield-svg">
                                <path d="M30 5L50 15v25c0 15-20 25-20 25S10 55 10 40V15L30 5z" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
                                <path d="M20 30l8 8 16-16" stroke="#fff" stroke-width="3" fill="none"/>
                            </svg>
                        </div>

                        <div class="people">
                            <div class="person person-1">
                                <div class="person-head"></div>
                                <div class="person-body"></div>
                            </div>
                            <div class="person person-2">
                                <div class="person-head"></div>
                                <div class="person-body"></div>
                            </div>
                        </div>

                        <div class="medical-bag">
                            <div class="bag-body"></div>
                            <div class="bag-handle"></div>
                            <div class="cross">+</div>
                        </div>

                        <div class="floating-elements">
                            <div class="dot dot-1"></div>
                            <div class="dot dot-2"></div>
                            <div class="dot dot-3"></div>
                            <div class="plus plus-1">+</div>
                            <div class="plus plus-2">+</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="container">
            <div class="services-header">
                <h2 class="section-title">Our services</h2>
                <div class="title-underline"></div>
                <p class="section-description">
                    We provide to you the best choices for you. Adjust it to your health needs and make sure your undergo treatment with our highly qualified doctors you can consult with us which type of service is suitable for your health
                </p>
            </div>

            <!-- Services Grid -->
            <div class="services-grid">
                <!-- Search Doctor -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <circle cx="40" cy="40" r="25" stroke="#458FF6" stroke-width="4" fill="none"/>
                            <path d="M65 65L80 80" stroke="#458FF6" stroke-width="4"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Search doctor</h3>
                    <p class="service-description">Choose your doctor from thousands of specialist, general, and trusted hospitals</p>
                </div>

                <!-- Online Pharmacy -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <rect x="25" y="30" width="50" height="60" rx="5" fill="#458FF6"/>
                            <rect x="30" y="20" width="40" height="15" rx="3" fill="#458FF6"/>
                            <circle cx="40" cy="50" r="8" fill="white"/>
                            <circle cx="60" cy="50" r="8" fill="white"/>
                            <rect x="35" y="65" width="30" height="3" fill="white"/>
                            <rect x="35" y="72" width="20" height="3" fill="white"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Online pharmacy</h3>
                    <p class="service-description">Buy your medicines with our mobile application with a simple delivery system</p>
                </div>

                <!-- Consultation -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <rect x="20" y="25" width="60" height="50" rx="5" fill="#458FF6"/>
                            <rect x="25" y="30" width="50" height="35" fill="white"/>
                            <rect x="30" y="35" width="15" height="15" rx="7" fill="#458FF6"/>
                            <rect x="50" y="37" width="20" height="3" fill="#458FF6"/>
                            <rect x="50" y="42" width="15" height="3" fill="#458FF6"/>
                            <rect x="30" y="55" width="40" height="3" fill="#458FF6"/>
                            <rect x="30" y="60" width="30" height="3" fill="#458FF6"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Consultation</h3>
                    <p class="service-description">Free consultation with our trusted doctors and get the best recommendations</p>
                </div>

                <!-- Details Info -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <rect x="25" y="20" width="50" height="65" rx="5" stroke="#458FF6" stroke-width="3" fill="none"/>
                            <rect x="30" y="30" width="40" height="3" fill="#458FF6"/>
                            <rect x="30" y="38" width="30" height="3" fill="#458FF6"/>
                            <rect x="30" y="46" width="35" height="3" fill="#458FF6"/>
                            <polyline points="30,55 35,60 45,50" stroke="#458FF6" stroke-width="3" fill="none"/>
                            <rect x="50" y="55" width="20" height="3" fill="#458FF6"/>
                            <rect x="30" y="65" width="25" height="3" fill="#458FF6"/>
                            <rect x="30" y="73" width="20" height="3" fill="#458FF6"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Details info</h3>
                    <p class="service-description">Free consultation with our trusted doctors and get the best recommendations</p>
                </div>

                <!-- Emergency Care -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <rect x="30" y="25" width="40" height="50" rx="5" fill="#458FF6"/>
                            <rect x="25" y="35" width="50" height="10" fill="#458FF6"/>
                            <rect x="45" y="15" width="10" height="70" fill="#458FF6"/>
                            <circle cx="50" cy="50" r="15" fill="white"/>
                            <rect x="45" y="40" width="10" height="20" fill="#458FF6"/>
                            <rect x="40" y="45" width="20" height="10" fill="#458FF6"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Emergency care</h3>
                    <p class="service-description">You can get 24/7 urgent care for yourself or your children and your lovely family</p>
                </div>

                <!-- Tracking -->
                <div class="service-card">
                    <div class="service-icon">
                        <svg viewBox="0 0 100 100" class="icon-svg">
                            <rect x="20" y="25" width="60" height="50" rx="5" fill="#458FF6"/>
                            <rect x="25" y="30" width="50" height="35" fill="white"/>
                            <circle cx="35" cy="40" r="3" fill="#458FF6"/>
                            <rect x="42" y="37" width="25" height="2" fill="#458FF6"/>
                            <rect x="42" y="41" width="20" height="2" fill="#458FF6"/>
                            <circle cx="35" cy="50" r="3" fill="#458FF6"/>
                            <rect x="42" y="47" width="25" height="2" fill="#458FF6"/>
                            <rect x="42" y="51" width="15" height="2" fill="#458FF6"/>
                            <circle cx="35" cy="60" r="3" fill="#458FF6"/>
                            <rect x="42" y="57" width="20" height="2" fill="#458FF6"/>
                            <rect x="42" y="61" width="25" height="2" fill="#458FF6"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Tracking</h3>
                    <p class="service-description">Track and save your medical history and health data</p>
                </div>
            </div>

            <!-- Learn More Button -->
            <div class="services-footer">
                <button class="btn-secondary">Learn more</button>
            </div>
        </div>
    </section>


</body>
</html>
